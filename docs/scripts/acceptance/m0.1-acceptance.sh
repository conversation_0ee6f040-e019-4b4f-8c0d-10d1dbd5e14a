#!/usr/bin/env bash
set -euo pipefail

echo "📚 Running M0.1 Docusaurus Documentation Site Acceptance Tests..."

# Test 1: Verify docs site directory structure
echo "1️⃣ Testing docs site directory structure..."
if [[ ! -d "code/apps/docs-site" ]]; then
    echo "❌ Docusaurus site directory not found: code/apps/docs-site"
    exit 1
fi

if [[ ! -f "code/apps/docs-site/docusaurus.config.js" ]]; then
    echo "❌ Docusaurus config not found: code/apps/docs-site/docusaurus.config.js"
    exit 1
fi

if [[ ! -f "code/apps/docs-site/package.json" ]]; then
    echo "❌ Docs site package.json not found"
    exit 1
fi
echo "✅ Docs site directory structure verified"

# Test 2: Install docs site dependencies
echo "2️⃣ Testing docs site dependency installation..."
cd code/apps/docs-site
pnpm install
echo "✅ Docs site dependencies installed"

# Test 3: Build docs site
echo "3️⃣ Testing docs site build..."
pnpm run build
if [[ ! -d "build" ]]; then
    echo "❌ Build directory not created"
    exit 1
fi
echo "✅ Docs site build successful"

# Test 4: Verify build output contains expected content
echo "4️⃣ Testing build output content..."
if [[ ! -f "build/index.html" ]]; then
    echo "❌ Main index.html not found in build output"
    exit 1
fi

# Check if tech-specs content is included
if ! find build -name "*.html" -exec grep -l "tech-spec" {} \; | head -1 > /dev/null; then
    echo "❌ Tech specs content not found in build output"
    exit 1
fi
echo "✅ Build output contains expected content"

# Test 5: Test local development server
echo "5️⃣ Testing local development server..."
pnpm run start &
SERVER_PID=$!

# Wait for server to start
sleep 10

# Test if server is responding
if curl -fs http://localhost:3000 > /dev/null; then
    echo "✅ Development server is responding"
else
    echo "❌ Development server failed to start or respond"
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi

# Test if tech specs are accessible
if curl -fs http://localhost:3000/tech-spec/structure > /dev/null; then
    echo "✅ Tech specs content accessible via server"
else
    echo "⚠️  Tech specs content not accessible (may be expected if not fully configured)"
fi

# Clean up server
kill $SERVER_PID 2>/dev/null || true
sleep 2

# Test 6: Verify root package.json scripts
echo "6️⃣ Testing root package.json scripts..."
cd ../../..

if ! grep -q '"docs:build"' package.json; then
    echo "❌ docs:build script not found in root package.json"
    exit 1
fi

if ! grep -q '"docs:start"' package.json; then
    echo "❌ docs:start script not found in root package.json"
    exit 1
fi
echo "✅ Root package.json scripts verified"

# Test 7: Test root scripts work
echo "7️⃣ Testing root docs scripts..."
pnpm run docs:build
echo "✅ Root docs:build script works"

# Test 8: Verify CI workflow exists
echo "8️⃣ Testing CI workflow configuration..."
if [[ ! -f ".github/workflows/docs.yml" ]]; then
    echo "❌ Docs CI workflow not found: .github/workflows/docs.yml"
    exit 1
fi

# Check if workflow contains expected jobs
if ! grep -q "build-docs" .github/workflows/docs.yml; then
    echo "❌ build-docs job not found in docs.yml"
    exit 1
fi
echo "✅ CI workflow configuration verified"

# Test 9: Spec validation
echo "9️⃣ Testing specification validation..."
node docs/scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx
echo "✅ Specification validation passed"

echo "🎉 All M0.1 acceptance tests passed!"
echo "✅ Docusaurus documentation site is ready for use"
